#!/usr/bin/env python3
"""
Helper script to set up the .env file for Browserbase credentials.
"""

import os
import sys
from pathlib import Path


def setup_env_file():
    """Set up the .env file with user input."""
    print("🔧 Browserbase Environment Setup")
    print("=" * 40)
    print()
    print("This script will help you create a .env file with your Browserbase credentials.")
    print("You can get your API key and project ID from https://browserbase.com")
    print()
    
    # Check if .env already exists
    env_file = Path('.env')
    if env_file.exists():
        print("⚠️  .env file already exists!")
        response = input("Do you want to overwrite it? (y/N): ").lower().strip()
        if response != 'y':
            print("Setup cancelled.")
            return False
    
    # Get credentials from user
    print("Please enter your Browserbase credentials:")
    print()
    
    api_key = input("Browserbase API Key: ").strip()
    if not api_key:
        print("❌ API key is required!")
        return False
    
    project_id = input("Browserbase Project ID: ").strip()
    if not project_id:
        print("❌ Project ID is required!")
        return False
    
    # Optional settings
    print()
    print("Optional settings (press Enter to use defaults):")
    
    max_sessions = input("Max concurrent sessions (default: 10): ").strip()
    if not max_sessions:
        max_sessions = "10"
    
    base_url = input("API base URL (default: https://api.browserbase.com): ").strip()
    if not base_url:
        base_url = "https://api.browserbase.com"
    
    # Create .env file
    env_content = f"""# Browserbase Configuration
# Generated by setup_env.py
BROWSERBASE_API_KEY={api_key}
BROWSERBASE_PROJECT_ID={project_id}
BROWSERBASE_MAX_SESSIONS={max_sessions}
BROWSERBASE_BASE_URL={base_url}
"""
    
    try:
        with open('.env', 'w') as f:
            f.write(env_content)
        
        print()
        print("✅ .env file created successfully!")
        print()
        print("Your configuration:")
        print(f"  API Key: {api_key[:8]}...{api_key[-4:] if len(api_key) > 12 else '***'}")
        print(f"  Project ID: {project_id}")
        print(f"  Max Sessions: {max_sessions}")
        print(f"  Base URL: {base_url}")
        print()
        print("The .env file is automatically ignored by git for security.")
        print("You can now run the scraping CLI!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating .env file: {e}")
        return False


def main():
    """Main function."""
    try:
        success = setup_env_file()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\nSetup cancelled by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main() 